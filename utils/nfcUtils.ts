import { StudentData, Transaction } from "@/types/student";
import AsyncStorage from "@react-native-async-storage/async-storage";

const TRANSACTIONS_KEY = "nfc_transactions";
const TERMINAL_ID_KEY = "terminal_id";

export class TransactionLogger {
  static async logTransaction(transaction: Transaction): Promise<void> {
    try {
      const existingTransactions = await this.getTransactions();
      const updatedTransactions = [...existingTransactions, transaction];
      await AsyncStorage.setItem(
        TRANSACTIONS_KEY,
        JSON.stringify(updatedTransactions)
      );
    } catch (error) {
      console.error("Failed to log transaction:", error);
      throw new Error("Failed to save transaction log");
    }
  }

  static async getTransactions(): Promise<Transaction[]> {
    try {
      const transactions = await AsyncStorage.getItem(TRANSACTIONS_KEY);
      return transactions ? JSON.parse(transactions) : [];
    } catch (error) {
      console.error("Failed to get transactions:", error);
      return [];
    }
  }

  static async getTodaysTransactions(): Promise<Transaction[]> {
    const allTransactions = await this.getTransactions();
    const today = new Date().toDateString();
    return allTransactions.filter(
      (t) => new Date(t.timestamp).toDateString() === today
    );
  }

  static async clearTransactions(): Promise<void> {
    try {
      await AsyncStorage.removeItem(TRANSACTIONS_KEY);
    } catch (error) {
      console.error("Failed to clear transactions:", error);
    }
  }

  static async setTerminalId(terminalId: string): Promise<void> {
    try {
      await AsyncStorage.setItem(TERMINAL_ID_KEY, terminalId);
    } catch (error) {
      console.error("Failed to set terminal ID:", error);
    }
  }

  static async getTerminalId(): Promise<string> {
    try {
      const terminalId = await AsyncStorage.getItem(TERMINAL_ID_KEY);
      return terminalId || "terminal_1";
    } catch (error) {
      console.error("Failed to get terminal ID:", error);
      return "terminal_1";
    }
  }
}

export class NFCErrorHandler {
  static getErrorMessage(error: unknown): string {
    if (!(error instanceof Error)) {
      return "Unknown error occurred";
    }

    const message = error.message.toLowerCase();

    // Common NFC error patterns and user-friendly messages
    if (message.includes("no ndef message found")) {
      return "This card doesn't contain student data. Please use a properly formatted student card or create a new one.";
    }

    if (
      message.includes("tag was lost") ||
      message.includes("tag connection lost")
    ) {
      return "Card was removed too quickly. Please keep the card near your device until the operation completes.";
    }

    if (message.includes("nfc not supported")) {
      return "NFC is not supported on this device.";
    }

    if (message.includes("nfc not enabled")) {
      return "NFC is disabled. Please enable NFC in your device settings.";
    }

    if (message.includes("insufficient balance")) {
      return "Insufficient balance on the card for this transaction.";
    }

    if (message.includes("invalid student data")) {
      return "The card contains invalid or corrupted student data. Please contact support.";
    }

    if (message.includes("timeout") || message.includes("timed out")) {
      return "Operation timed out. Please try again and keep the card close to your device.";
    }

    if (message.includes("cancelled") || message.includes("canceled")) {
      return "Operation was cancelled.";
    }

    if (message.includes("write") && message.includes("failed")) {
      return "Failed to write to card. The card may be read-only or damaged.";
    }

    if (message.includes("read") && message.includes("failed")) {
      return "Failed to read from card. Please try again or use a different card.";
    }

    // Return the original error message if no specific pattern matches
    return error.message;
  }

  static getErrorTitle(error: unknown): string {
    if (!(error instanceof Error)) {
      return "Error";
    }

    const message = error.message.toLowerCase();

    if (message.includes("no ndef message found")) {
      return "Invalid Card";
    }

    if (
      message.includes("tag was lost") ||
      message.includes("tag connection lost")
    ) {
      return "Card Removed";
    }

    if (
      message.includes("nfc not supported") ||
      message.includes("nfc not enabled")
    ) {
      return "NFC Issue";
    }

    if (message.includes("insufficient balance")) {
      return "Insufficient Balance";
    }

    if (message.includes("timeout") || message.includes("timed out")) {
      return "Timeout";
    }

    if (message.includes("write") && message.includes("failed")) {
      return "Write Failed";
    }

    if (message.includes("read") && message.includes("failed")) {
      return "Read Failed";
    }

    return "Error";
  }
}

export class NFCDataHandler {
  static encodeStudentData(data: StudentData): string {
    return JSON.stringify(data);
  }

  static decodeStudentData(encodedData: string): StudentData {
    try {
      const parsed = JSON.parse(encodedData);
      return {
        student_id: parsed.student_id || "",
        name: parsed.name || "",
        balance: parseFloat(parsed.balance) || 0,
        txn_count: parseInt(parsed.txn_count) || 0,
      };
    } catch {
      throw new Error("Invalid student data format");
    }
  }

  static async safeNfcOperation<T>(operation: () => Promise<T>): Promise<T> {
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        // Cancel any existing technology requests
        try {
          await import("react-native-nfc-manager").then(
            ({ default: NfcManager }) => NfcManager.cancelTechnologyRequest()
          );
        } catch {
          // Ignore cancel errors
        }

        // Add small delay before retry
        if (retryCount > 0) {
          await new Promise((resolve) => setTimeout(resolve, 500));
        }

        return await operation();
      } catch (error) {
        retryCount++;
        console.log(`NFC operation attempt ${retryCount} failed:`, error);

        if (retryCount >= maxRetries) {
          throw error;
        }

        // If it's a permission or tag lost error, don't retry
        const errorMessage =
          error instanceof Error ? error.message.toLowerCase() : "";
        if (
          errorMessage.includes("permission") ||
          errorMessage.includes("tag was lost") ||
          errorMessage.includes("out of date")
        ) {
          throw error;
        }
      }
    }

    throw new Error("NFC operation failed after retries");
  }

  static validateStudentData(data: StudentData): boolean {
    return !!(
      data.student_id &&
      data.name &&
      typeof data.balance === "number" &&
      typeof data.txn_count === "number" &&
      data.balance >= 0
    );
  }

  static processTransaction(
    studentData: StudentData,
    amount: number,
    terminalId: string
  ): { updatedData: StudentData; transaction: Transaction } {
    if (studentData.balance < amount) {
      throw new Error("Insufficient balance");
    }

    const oldBalance = studentData.balance;
    const newBalance = oldBalance - amount;
    const newTxnCount = studentData.txn_count + 1;

    const updatedData: StudentData = {
      ...studentData,
      balance: newBalance,
      txn_count: newTxnCount,
    };

    const transaction: Transaction = {
      id: `${terminalId}_${Date.now()}_${studentData.student_id}`,
      timestamp: new Date().toISOString(),
      student_id: studentData.student_id,
      student_name: studentData.name,
      old_balance: oldBalance,
      new_balance: newBalance,
      amount_deducted: amount,
      terminal_id: terminalId,
    };

    return { updatedData, transaction };
  }
}

export class NFCTimeout {
  private static timeoutId: ReturnType<typeof setTimeout> | null = null;
  private static onTimeoutCallback: (() => void) | null = null;

  static startTimeout(timeoutMs: number = 15000, onTimeout?: () => void): void {
    this.clearTimeout();
    this.onTimeoutCallback = onTimeout || null;

    this.timeoutId = setTimeout(() => {
      console.log("NFC operation timed out");
      if (this.onTimeoutCallback) {
        this.onTimeoutCallback();
      }
    }, timeoutMs);
  }

  static clearTimeout(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
    this.onTimeoutCallback = null;
  }
}

export class NFCScanningSession {
  private static isScanning: boolean = false;
  private static timeoutId: ReturnType<typeof setTimeout> | null = null;
  private static onTimeoutCallback: (() => void) | null = null;
  private static currentTechnology: any = null;
  private static sessionData: any = null;
  private static sessionId: string | null = null;
  private static cleanupPromise: Promise<void> | null = null;

  static async startScanningSession(
    timeoutMs: number = 15000,
    onTimeout?: () => void
  ): Promise<void> {
    // Wait for any ongoing cleanup to complete
    if (this.cleanupPromise) {
      await this.cleanupPromise;
    }

    // Force stop any existing session
    if (this.isScanning) {
      await this.stopScanningSession();
    }

    // Generate unique session ID
    this.sessionId = `session_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 11)}`;
    const currentSessionId = this.sessionId;

    this.isScanning = true;
    this.onTimeoutCallback = onTimeout || null;
    this.currentTechnology = null;
    this.sessionData = null;

    console.log(`Starting NFC scanning session: ${currentSessionId}`);

    // Start timeout
    this.timeoutId = setTimeout(() => {
      // Only timeout if this is still the current session
      if (this.sessionId === currentSessionId && this.isScanning) {
        console.log(`NFC scanning session timed out: ${currentSessionId}`);
        this.stopScanningSession();
        if (this.onTimeoutCallback) {
          this.onTimeoutCallback();
        }
      }
    }, timeoutMs);
  }

  static async stopScanningSession(): Promise<void> {
    if (!this.isScanning && !this.timeoutId) {
      return; // Already stopped
    }

    const sessionId = this.sessionId;
    console.log(`Stopping NFC scanning session: ${sessionId}`);

    this.isScanning = false;

    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

    this.onTimeoutCallback = null;
    this.currentTechnology = null;
    this.sessionData = null;
    this.sessionId = null;

    // Create cleanup promise to prevent race conditions
    this.cleanupPromise = this.performCleanup();
    await this.cleanupPromise;
    this.cleanupPromise = null;
  }

  private static async performCleanup(): Promise<void> {
    // Cancel any ongoing NFC operations with retry logic
    let retryCount = 0;
    const maxRetries = 3;

    while (retryCount < maxRetries) {
      try {
        const { default: NfcManager } = await import(
          "react-native-nfc-manager"
        );
        await NfcManager.cancelTechnologyRequest();
        console.log("NFC technology request canceled successfully");
        break;
      } catch (error) {
        retryCount++;
        console.log(
          `Error canceling NFC technology request (attempt ${retryCount}):`,
          error
        );

        if (retryCount < maxRetries) {
          // Wait before retrying
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }
    }

    // Also cleanup unified operations
    try {
      await NFCUnifiedOperations.endCardSession();
    } catch (error) {
      console.log("Error ending unified card session:", error);
    }
  }

  static isSessionActive(): boolean {
    return this.isScanning;
  }

  static getSessionId(): string | null {
    return this.sessionId;
  }

  static setSessionData(data: any): void {
    if (this.isScanning) {
      this.sessionData = data;
    }
  }

  static getSessionData(): any {
    return this.isScanning ? this.sessionData : null;
  }

  static setCurrentTechnology(tech: any): void {
    if (this.isScanning) {
      this.currentTechnology = tech;
    }
  }

  static getCurrentTechnology(): any {
    return this.isScanning ? this.currentTechnology : null;
  }

  static async forceReset(): Promise<void> {
    console.log("Force resetting NFC scanning session");
    this.isScanning = false;

    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

    this.onTimeoutCallback = null;
    this.currentTechnology = null;
    this.sessionData = null;
    this.sessionId = null;

    await this.performCleanup();
  }
}

// Unified NFC operations for smooth read-hold-write flow
export class NFCUnifiedOperations {
  private static currentSession: {
    nfcManager: any;
    technology: any;
    cardData?: StudentData;
  } | null = null;

  static async startCardSession(): Promise<StudentData> {
    const { default: NfcManager, NfcTech } = await import(
      "react-native-nfc-manager"
    );

    // Clean up any existing session
    await this.endCardSession();

    let lastError: any = null;

    // Try NDEF first (most common format)
    try {
      await NfcManager.requestTechnology([NfcTech.Ndef]);
      const tag = await NfcManager.getTag();

      if (tag?.ndefMessage && tag.ndefMessage.length > 0) {
        const record = tag.ndefMessage[0];
        const payload = record.payload;

        // Handle different text record formats
        let studentDataString = "";
        if (payload[0] === 0x02) {
          // Text record with language code
          const languageCodeLength = payload[0] & 0x3f;
          const text = payload.slice(languageCodeLength + 1);
          studentDataString = String.fromCharCode(...text);
        } else if (payload.length > 3) {
          // Skip language code prefix for text records
          const languageCodeLength = payload[0] & 0x3f;
          const text = payload.slice(languageCodeLength + 1);
          studentDataString = String.fromCharCode(...text);
        } else {
          // Direct text payload
          studentDataString = String.fromCharCode(...payload);
        }

        const cardData = NFCDataHandler.decodeStudentData(studentDataString);

        // Store session data
        this.currentSession = {
          nfcManager: NfcManager,
          technology: NfcTech.Ndef,
          cardData,
        };

        return cardData;
      } else {
        throw new Error("No NDEF message found on card");
      }
    } catch (ndefError) {
      console.log("NDEF read failed:", ndefError);
      lastError = ndefError;
      await NfcManager.cancelTechnologyRequest().catch(() => {});
    }

    // Try NfcA technology as fallback
    try {
      await NfcManager.requestTechnology([NfcTech.NfcA]);
      const tag = await NfcManager.getTag();
      await NfcManager.cancelTechnologyRequest().catch(() => {});

      if (tag) {
        throw new Error(
          "This card type is not supported for student data storage. Please use an NDEF-formatted card."
        );
      }
    } catch (nfcAError) {
      console.log("NfcA read failed:", nfcAError);
      await NfcManager.cancelTechnologyRequest().catch(() => {});
    }

    // Provide more specific error messages
    if (lastError) {
      const errorMessage = lastError.message || lastError.toString();
      if (
        errorMessage.includes("Tag was lost") ||
        errorMessage.includes("out of date")
      ) {
        throw new Error(
          "Card connection lost. Please hold the card steady and try again."
        );
      } else if (errorMessage.includes("Permission")) {
        throw new Error(
          "NFC permission denied. Please enable NFC in your device settings."
        );
      } else if (errorMessage.includes("no tech request available")) {
        throw new Error(
          "NFC not available. Please ensure NFC is enabled on your device."
        );
      }
    }

    throw new Error(
      "Unable to read student data from this card. Please ensure the card is properly formatted with student data."
    );
  }

  static async writeToCurrentSession(data: StudentData): Promise<void> {
    if (!this.currentSession) {
      throw new Error("No active card session. Please scan the card first.");
    }

    const { nfcManager: NfcManager } = this.currentSession;
    const { Ndef, NfcTech } = await import("react-native-nfc-manager");

    const dataString = NFCDataHandler.encodeStudentData(data);
    const bytes = Ndef.encodeMessage([Ndef.textRecord(dataString)]);

    if (!bytes) {
      throw new Error("Failed to encode NDEF message");
    }

    try {
      // Use the existing session to write
      await NfcManager.ndefHandler.writeNdefMessage(bytes);
      console.log("Successfully wrote to card using existing session");

      // Update session data
      this.currentSession.cardData = data;
    } catch (error) {
      console.log("Write failed with existing session:", error);

      // Try to format the card if write fails
      try {
        console.log("Attempting to format card...");
        await NfcManager.cancelTechnologyRequest().catch(() => {});
        await NfcManager.requestTechnology([NfcTech.NdefFormatable]);
        await NfcManager.ndefFormatableHandlerAndroid.formatNdef(bytes);
        console.log("Successfully formatted and wrote to card");

        // Update session data
        this.currentSession.cardData = data;
        return;
      } catch (formatError) {
        console.log("Format failed:", formatError);
        await NfcManager.cancelTechnologyRequest().catch(() => {});
      }

      // Provide more specific error messages
      const errorMessage =
        error instanceof Error
          ? error.message
          : error?.toString() || "Unknown error";
      if (
        errorMessage.includes("Tag was lost") ||
        errorMessage.includes("out of date")
      ) {
        throw new Error(
          "Card connection lost during write. Please hold the card steady and try again."
        );
      } else if (errorMessage.includes("Permission")) {
        throw new Error(
          "Permission denied. Please ensure NFC permissions are granted."
        );
      } else if (
        errorMessage.includes("read-only") ||
        errorMessage.includes("not writable")
      ) {
        throw new Error(
          "This card is read-only and cannot be written to. Please use a writable NFC card."
        );
      }

      throw new Error(
        "Unable to write to this card. The card may not be writable or compatible."
      );
    }
  }

  static async endCardSession(): Promise<void> {
    if (this.currentSession) {
      try {
        await this.currentSession.nfcManager.cancelTechnologyRequest();
      } catch (error) {
        console.log("Error ending card session:", error);
      }
      this.currentSession = null;
    }
  }

  static isSessionActive(): boolean {
    return this.currentSession !== null;
  }

  static getSessionData(): StudentData | undefined {
    return this.currentSession?.cardData;
  }
}

// Enhanced NFC utilities for better card compatibility
export class NFCCardOperations {
  static async readCardWithFallback(): Promise<StudentData> {
    const { default: NfcManager, NfcTech } = await import(
      "react-native-nfc-manager"
    );

    // Try NDEF first (most common format)
    try {
      await NfcManager.requestTechnology([NfcTech.Ndef]);
      const tag = await NfcManager.getTag();

      if (tag?.ndefMessage && tag.ndefMessage.length > 0) {
        const record = tag.ndefMessage[0];
        const payload = record.payload;

        // Handle different text record formats
        let studentDataString = "";
        if (payload[0] === 0x02) {
          // Text record with language code
          const languageCodeLength = payload[0] & 0x3f;
          const text = payload.slice(languageCodeLength + 1);
          studentDataString = String.fromCharCode(...text);
        } else {
          // Direct text payload
          studentDataString = String.fromCharCode(...payload);
        }

        return NFCDataHandler.decodeStudentData(studentDataString);
      }
    } catch (ndefError) {
      console.log("NDEF read failed:", ndefError);
      await NfcManager.cancelTechnologyRequest().catch(() => {});
    }

    // Try NfcA technology for other card types
    try {
      await NfcManager.requestTechnology([NfcTech.NfcA]);
      // For NfcA cards, we would need to implement custom reading
      throw new Error("NfcA cards are not supported for student data storage");
    } catch (nfcAError) {
      console.log("NfcA read failed:", nfcAError);
      await NfcManager.cancelTechnologyRequest().catch(() => {});
    }

    throw new Error(
      "Unable to read student data from this card. Please ensure the card is properly formatted with student data."
    );
  }

  static async writeCardWithFallback(data: StudentData): Promise<void> {
    const {
      default: NfcManager,
      Ndef,
      NfcTech,
    } = await import("react-native-nfc-manager");

    const dataString = NFCDataHandler.encodeStudentData(data);
    const bytes = Ndef.encodeMessage([Ndef.textRecord(dataString)]);

    if (!bytes) {
      throw new Error("Failed to encode NDEF message");
    }

    // Try writing to already formatted NDEF cards
    try {
      await NfcManager.requestTechnology([NfcTech.Ndef]);
      await NfcManager.ndefHandler.writeNdefMessage(bytes);
      console.log("Successfully wrote to NDEF card");
      return;
    } catch (error) {
      console.log("NDEF write failed:", error);
      await NfcManager.cancelTechnologyRequest().catch(() => {});
    }

    // Try formatting blank cards
    try {
      await NfcManager.requestTechnology([NfcTech.NdefFormatable]);
      await NfcManager.ndefFormatableHandlerAndroid.formatNdef(bytes);
      console.log("Successfully formatted and wrote to card");
      return;
    } catch (error) {
      console.log("Format failed:", error);
      await NfcManager.cancelTechnologyRequest().catch(() => {});
    }

    throw new Error(
      "Unable to write to this card. Please use an NDEF-compatible NFC card or tag."
    );
  }

  static async checkCardCompatibility(): Promise<string> {
    const { default: NfcManager, NfcTech } = await import(
      "react-native-nfc-manager"
    );

    try {
      await NfcManager.requestTechnology([NfcTech.Ndef]);
      const tag = await NfcManager.getTag();
      await NfcManager.cancelTechnologyRequest().catch(() => {});

      if (tag?.ndefMessage && tag.ndefMessage.length > 0) {
        return "NDEF card with data";
      }
      return "NDEF card (empty)";
    } catch {
      try {
        await NfcManager.requestTechnology([NfcTech.NdefFormatable]);
        await NfcManager.cancelTechnologyRequest().catch(() => {});
        return "Formatable card";
      } catch {
        try {
          await NfcManager.requestTechnology([NfcTech.NfcA]);
          await NfcManager.cancelTechnologyRequest().catch(() => {});
          return "NfcA card (limited compatibility)";
        } catch {
          return "Unsupported card type";
        }
      }
    }
  }
}

// NFC Troubleshooting and Tips
export class NFCTroubleshootingTips {
  static getPermissionErrorTips(): string[] {
    return [
      "Make sure NFC is enabled in your device settings",
      "Check that your app has NFC permissions in Settings > Apps > [App Name] > Permissions",
      "Restart your device if NFC permissions were recently granted",
      "Try closing and reopening the app",
      "Some devices require you to unlock the screen before NFC operations",
    ];
  }

  static getCardReadErrorTips(): string[] {
    return [
      "Hold the card steady against the NFC area for 2-3 seconds",
      "Make sure the card is an NFC-enabled card",
      "Try different positions on the back of your device",
      "Remove any thick cases or metal objects between the card and device",
      "Check if the card is damaged or demagnetized",
      "Try a different NFC card to verify your device is working",
    ];
  }

  static getCardWriteErrorTips(): string[] {
    return [
      "Use only NDEF-compatible NFC cards or tags",
      "Ensure the card is not write-protected",
      "Hold the card steady until the write operation completes",
      "Try formatting the card first if it's a new, blank card",
      "Some cheap cards may not support writing - try a different card",
      "Check if the card has enough memory for the student data",
    ];
  }

  static getDeviceSpecificTips(): string[] {
    return [
      "Samsung devices: Try disabling 'NFC service' and re-enabling it",
      "Older Android devices: May require manual NFC positioning",
      "Some devices: NFC area may be in the center rather than top/bottom",
      "Try airplane mode on/off to reset NFC radio",
      "Clear NFC cache in Settings > Apps > NFC Service > Storage",
    ];
  }
}
