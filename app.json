{"expo": {"name": "nfctest", "slug": "nfctest", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "nfctest", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NFCReaderUsageDescription": "This app needs access to NFC to read and write student credit cards", "com.apple.developer.nfc.readersession.formats": ["NDEF", "TAG"]}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.emacliam.nfctest", "permissions": ["android.permission.NFC"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}}}