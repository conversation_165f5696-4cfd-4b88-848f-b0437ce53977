export interface StudentData {
  student_id: string;
  name: string;
  balance: number;
  txn_count: number;
}

export interface Transaction {
  id: string;
  timestamp: string;
  student_id: string;
  student_name: string;
  old_balance: number;
  new_balance: number;
  amount_deducted: number;
  terminal_id: string;
}

export interface Terminal {
  id: string;
  name: string;
  transaction_cost: number;
}

export const DEFAULT_TERMINALS: Terminal[] = [
  { id: "terminal_1", name: "Cafeteria", transaction_cost: 5.0 },
  { id: "terminal_2", name: "Library", transaction_cost: 2.5 },
  { id: "terminal_3", name: "Gym", transaction_cost: 3.0 },
];
