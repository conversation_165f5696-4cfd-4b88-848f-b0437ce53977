# NFC Student Credit Management System

An offline React Native app for managing student credit balances using NFC cards. Each student has an NFC card that stores their ID, name, current balance, and transaction count. The system works completely offline with multiple terminal support.

## Features

- **NFC Card Reading/Writing**: Read and update student data directly on NFC cards
- **Multiple Terminals**: Support for different terminals (Cafeteria, Library, Gym) with different transaction costs
- **Offline Operation**: No internet connection required during transactions
- **Local Transaction Logging**: All transactions stored locally for daily reconciliation
- **Student Card Creation**: Create new student cards with initial balance
- **Transaction History**: View all transactions with detailed information
- **Card Compatibility Checker**: Test NFC card compatibility before use
- **Troubleshooting Guide**: Built-in help for common NFC issues

## Setup

### Prerequisites

- React Native development environment
- Android device with NFC capability OR iOS device with NFC capability
- NDEF-compatible NFC cards (MIFARE Classic, DESFire, or NTAG series recommended)

### Installation

1. Install dependencies:

```bash
npm install
```

2. For iOS development, ensure you have the necessary NFC entitlements in your Apple Developer account.

3. Run the app:

```bash
# For Android
npm run android

# For iOS
npm run ios
```

## Troubleshooting NFC Issues

### Common Error Messages and Solutions

#### "Permission Denial: Tag is out of date"

- **Cause**: Android NFC permission or timing issue
- **Solution**:
  - Keep the card steady against the NFC area
  - Try restarting the app
  - Check NFC permissions in device settings
  - Ensure NFC is enabled in device settings

#### "Unable to write to this card"

- **Cause**: Card is not NDEF-compatible or is write-protected
- **Solution**:
  - Use NDEF-compatible NFC cards (MIFARE Classic, DESFire, NTAG series)
  - Check if card is write-protected
  - Try a different NFC card
  - Use the "Check Compatibility" feature to test cards

#### "NDEF read failed"

- **Cause**: Card is not formatted or doesn't contain student data
- **Solution**:
  - Create a new student card using the "Create Cards" feature
  - Use the "Check Compatibility" feature to verify the card
  - Try a different NFC card

#### "Format failed"

- **Cause**: Card cannot be formatted for NDEF use
- **Solution**:
  - Use a different NFC card
  - Ensure the card is NDEF-compatible
  - Try NTAG213/215/216 or MIFARE Classic cards

### Recommended NFC Cards

- **NTAG213/215/216**: Excellent compatibility, reliable
- **MIFARE Classic 1K/4K**: Good compatibility, widely available
- **MIFARE DESFire**: High security, good for institutional use
- **Avoid**: Generic/unknown NFC cards, cards labeled "NFC stickers" without NDEF support

### Device-Specific Tips

- **Samsung devices**: Try disabling and re-enabling NFC service
- **Older Android devices**: May require manual positioning of NFC area
- **All devices**: NFC area is usually on the back, try different positions
- **Performance**: Clear NFC cache in Settings > Apps > NFC Service > Storage

## Data Structure

Each NFC card stores student data in JSON format:

```json
{
  "student_id": "S12345",
  "name": "John Doe",
  "balance": 47.5,
  "txn_count": 12
}
```

## App Structure

### 1. Terminal Tab

- Select terminal type (Cafeteria $5.00, Library $2.50, Gym $3.00)
- Tap student cards to process transactions
- View current student information
- See transaction confirmation

### 2. Transactions Tab

- View today's transaction summary
- See complete transaction history
- Export transaction data as CSV
- Clear transaction logs

### 3. Cards Tab

- Create new student cards
- Generate sample student data
- Write student information to blank NFC cards

## Usage Workflow

### Creating Student Cards

1. Go to the "Cards" tab
2. Fill in student details:
   - Student ID (e.g., S12345)
   - Student Name (e.g., John Doe)
   - Initial Balance (e.g., 50.00)
3. Tap "Create Student Card"
4. Place a blank NFC card near your device when prompted
5. Wait for confirmation

### Processing Transactions

1. Go to the "Terminal" tab
2. Select the appropriate terminal
3. Tap "Tap Student Card"
4. Have the student place their NFC card near the device
5. Review transaction details and confirm
6. The card balance is updated automatically

### Viewing Transaction History

1. Go to the "Transactions" tab
2. View today's summary statistics
3. Scroll through transaction history
4. Export data or clear logs as needed

## Technical Details

### NFC Technology

- Uses NDEF (NFC Data Exchange Format) for data storage
- Text records store JSON-encoded student data
- Compatible with MIFARE Classic and DESFire cards

### Local Storage

- AsyncStorage for transaction logging
- No server dependency during operation
- Data persists between app sessions

### Error Handling

- Graceful handling of NFC read/write failures
- Retry mechanisms for failed operations
- User-friendly error messages

## Development

### Key Components

- `NFCTerminal.tsx` - Main terminal interface
- `TransactionLog.tsx` - Transaction history viewer
- `StudentCardManager.tsx` - Student card creation
- `nfcUtils.ts` - NFC data handling utilities
- `student.ts` - Type definitions

### Data Flow

1. Student taps card → Read current data
2. Validate balance → Process transaction
3. Update card data → Write back to card
4. Log transaction → Store locally

## Troubleshooting

### NFC Not Working

- Ensure NFC is enabled in device settings
- Check app permissions for NFC access
- Try different NFC cards if having issues

### Card Read Errors

- Ensure card is close to NFC antenna
- Hold card steady during read/write operations
- Check that card contains valid student data

### Transaction Failures

- Verify sufficient balance before transaction
- Ensure card data is not corrupted
- Check terminal settings and costs

## Hardware Requirements

### Supported NFC Cards

- MIFARE Classic 1K/4K
- MIFARE DESFire
- NTAG213/NTAG215/NTAG216
- Any NDEF-compatible NFC card

### Device Requirements

- Android: NFC-enabled device running Android 5.0+
- iOS: iPhone 7+ with iOS 13+ (Core NFC support)

## Security Notes

- Data stored in plain text on cards (no encryption in this version)
- Local transaction logs not encrypted
- Consider adding encryption for production use
- Physical card security is important

## Future Enhancements

- Data encryption on cards
- Server synchronization option
- Biometric authentication
- Receipt printing support
- Advanced reporting features
- Multi-language support
