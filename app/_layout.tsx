import {
  <PERSON><PERSON>hem<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@react-navigation/native";
import { useFonts } from "expo-font";
import * as NavigationBar from "expo-navigation-bar";
import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useEffect } from "react";
import NfcManager from "react-native-nfc-manager";
import "react-native-reanimated";

import { ThemedView } from "@/components/ThemedView";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useSafeAreaInsets } from "react-native-safe-area-context";

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const visibility = NavigationBar.useVisibility();
  const insets = useSafeAreaInsets();

  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  useEffect(() => {
    const setupNavigationBar = async () => {
      await NavigationBar.setVisibilityAsync("hidden");
    };

    const initNfc = async () => {
      try {
        const supported = await NfcManager.isSupported();
        if (supported) {
          await NfcManager.start();
          console.log("NFC initialized successfully");
        } else {
          console.log("NFC not supported on this device");
        }
      } catch (error) {
        console.error("NFC initialization failed:", error);
      }
    };

    setupNavigationBar();
    initNfc();

    return () => {
      // Cleanup NFC when app closes
      NfcManager.cancelTechnologyRequest().catch(() => {});
    };
  }, [visibility]);

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <ThemeProvider value={colorScheme === "dark" ? DarkTheme : DefaultTheme}>
      <ThemedView
        style={{ paddingTop: insets.top, backgroundColor: "#ffffff" }}
      />
      <Stack>
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="+not-found" />
      </Stack>
      <StatusBar style="auto" />
    </ThemeProvider>
  );
}
