import AppHeader from "@/components/ui/AppHeader";
import { IconSymbol } from "@/components/ui/IconSymbol";
import { Transaction } from "@/types/student";
import { TransactionLogger } from "@/utils/nfcUtils";
import React, { useEffect, useState } from "react";
import {
  <PERSON>ert,
  ScrollView,
  Share,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

interface TransactionItemProps {
  transaction: Transaction;
}

const TransactionItem: React.FC<TransactionItemProps> = ({ transaction }) => {
  const isCredit = transaction.amount_deducted < 0;
  const amount = Math.abs(transaction.amount_deducted);

  return (
    <View style={styles.transactionItem}>
      <View style={styles.transactionIcon}>
        <IconSymbol
          name={isCredit ? "plus.circle.fill" : "minus.circle.fill"}
          size={24}
          color={isCredit ? "#10b981" : "#ef4444"}
        />
      </View>

      <View style={styles.transactionDetails}>
        <Text style={styles.transactionTitle}>
          {isCredit ? "Credit Added" : "Transaction"}
        </Text>
        <Text style={styles.transactionSubtitle}>
          {transaction.student_name} • {transaction.terminal_id}
        </Text>
        <Text style={styles.transactionTime}>
          {new Date(transaction.timestamp).toLocaleString()}
        </Text>
      </View>

      <View style={styles.transactionAmount}>
        <Text
          style={[
            styles.amountText,
            { color: isCredit ? "#10b981" : "#ef4444" },
          ]}>
          {isCredit ? "+" : "-"}${amount.toFixed(2)}
        </Text>
        <Text style={styles.balanceText}>
          Balance: ${transaction.new_balance.toFixed(2)}
        </Text>
      </View>
    </View>
  );
};

export default function TransactionHistoryScreen() {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [todaysTransactions, setTodaysTransactions] = useState<Transaction[]>(
    []
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTransactions();
  }, []);

  const loadTransactions = async () => {
    try {
      setLoading(true);
      const allTransactions = await TransactionLogger.getTransactions();
      const todaysTxns = await TransactionLogger.getTodaysTransactions();

      // Sort by timestamp (newest first)
      allTransactions.sort(
        (a, b) =>
          new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
      );

      setTransactions(allTransactions);
      setTodaysTransactions(todaysTxns);
    } catch (error) {
      console.error("Error loading transactions:", error);
    } finally {
      setLoading(false);
    }
  };

  const exportTransactions = async () => {
    if (transactions.length === 0) {
      Alert.alert("No Data", "No transactions to export");
      return;
    }

    try {
      const csvHeader =
        "Date,Time,Student ID,Student Name,Terminal,Amount,Balance,Transaction ID\n";
      const csvData = transactions
        .map((t) => {
          const date = new Date(t.timestamp);
          return [
            date.toLocaleDateString(),
            date.toLocaleTimeString(),
            t.student_id,
            t.student_name,
            t.terminal_id,
            t.amount_deducted.toFixed(2),
            t.new_balance.toFixed(2),
            t.id,
          ].join(",");
        })
        .join("\n");

      const csvContent = csvHeader + csvData;

      await Share.share({
        message: csvContent,
        title: "Transaction History Export",
      });
    } catch (error) {
      console.error("Error exporting transactions:", error);
      Alert.alert("Export Failed", "Failed to export transaction data");
    }
  };

  const clearTransactions = () => {
    Alert.alert(
      "Clear All Transactions",
      "Are you sure you want to delete all transaction history? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Clear",
          style: "destructive",
          onPress: async () => {
            try {
              await TransactionLogger.clearTransactions();
              setTransactions([]);
              setTodaysTransactions([]);
              Alert.alert("Success", "All transactions have been cleared");
            } catch (error) {
              Alert.alert("Error", "Failed to clear transactions");
            }
          },
        },
      ]
    );
  };

  const totalAmount = todaysTransactions.reduce(
    (sum, t) => sum + Math.abs(t.amount_deducted),
    0
  );

  return (
    <View style={styles.container}>
      <AppHeader
        title="Transaction History"
        subtitle={`${transactions.length} total transactions`}
        rightComponent={
          <TouchableOpacity
            onPress={exportTransactions}
            style={styles.exportButton}>
            <IconSymbol name="square.and.arrow.up" size={20} color="#3b82f6" />
          </TouchableOpacity>
        }
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Today's Summary */}
        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Today's Summary</Text>
          <View style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryValue}>
                {todaysTransactions.length}
              </Text>
              <Text style={styles.summaryLabel}>Transactions</Text>
            </View>
            <View style={styles.summaryItem}>
              <Text style={styles.summaryValue}>${totalAmount.toFixed(2)}</Text>
              <Text style={styles.summaryLabel}>Total Amount</Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={loadTransactions}>
            <IconSymbol name="arrow.clockwise" size={16} color="#3b82f6" />
            <Text style={styles.actionButtonText}>Refresh</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.actionButton}
            onPress={clearTransactions}>
            <IconSymbol name="trash" size={16} color="#ef4444" />
            <Text style={[styles.actionButtonText, { color: "#ef4444" }]}>
              Clear All
            </Text>
          </TouchableOpacity>
        </View>

        {/* Transaction List */}
        {loading ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyTitle}>Loading...</Text>
          </View>
        ) : transactions.length === 0 ? (
          <View style={styles.emptyState}>
            <IconSymbol name="list.bullet" size={48} color="#94a3b8" />
            <Text style={styles.emptyTitle}>No Transactions</Text>
            <Text style={styles.emptySubtitle}>
              Transaction history will appear here after processing cards
            </Text>
          </View>
        ) : (
          <View style={styles.transactionList}>
            <Text style={styles.sectionTitle}>All Transactions</Text>
            {transactions.map((transaction) => (
              <TransactionItem key={transaction.id} transaction={transaction} />
            ))}
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },
  content: {
    flex: 1,
    padding: 20,
  },
  exportButton: {
    padding: 8,
  },
  summaryCard: {
    backgroundColor: "#ffffff",
    padding: 20,
    borderRadius: 16,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1e293b",
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  summaryItem: {
    alignItems: "center",
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: "700",
    color: "#3b82f6",
    marginBottom: 4,
  },
  summaryLabel: {
    fontSize: 14,
    color: "#64748b",
    fontWeight: "500",
  },
  actionButtons: {
    flexDirection: "row",
    gap: 12,
    marginBottom: 24,
  },
  actionButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#ffffff",
    padding: 12,
    borderRadius: 12,
    gap: 8,
    borderWidth: 1,
    borderColor: "#e2e8f0",
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#3b82f6",
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1e293b",
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: "#64748b",
    textAlign: "center",
    lineHeight: 24,
  },
  transactionList: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1e293b",
    marginBottom: 16,
  },
  transactionItem: {
    backgroundColor: "#ffffff",
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    flexDirection: "row",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  transactionIcon: {
    marginRight: 16,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1e293b",
    marginBottom: 4,
  },
  transactionSubtitle: {
    fontSize: 14,
    color: "#64748b",
    marginBottom: 2,
  },
  transactionTime: {
    fontSize: 12,
    color: "#94a3b8",
  },
  transactionAmount: {
    alignItems: "flex-end",
  },
  amountText: {
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 2,
  },
  balanceText: {
    fontSize: 12,
    color: "#64748b",
  },
});
