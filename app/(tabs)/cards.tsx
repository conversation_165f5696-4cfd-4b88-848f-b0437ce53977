import CardCompatibilityChecker from "@/components/CardCompatibilityChecker";
import StudentCardManager from "@/components/StudentCardManager";
import TroubleshootingGuide from "@/components/TroubleshootingGuide";
import AppHeader from "@/components/ui/AppHeader";
import React, { useState } from "react";
import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

export default function CardsScreen() {
  const [activeTab, setActiveTab] = useState<"create" | "check" | "help">(
    "create"
  );

  const getSubtitle = () => {
    switch (activeTab) {
      case "create":
        return "Create new student NFC cards";
      case "check":
        return "Check card compatibility";
      case "help":
        return "Get help with NFC issues";
      default:
        return "";
    }
  };

  return (
    <View style={styles.container}>
      <AppHeader title="NFC Cards" subtitle={getSubtitle()} />

      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.tabScrollView}
        contentContainerStyle={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === "create" && styles.activeTab]}
          onPress={() => setActiveTab("create")}>
          <Text
            style={[
              styles.tabText,
              activeTab === "create" && styles.activeTabText,
            ]}>
            Create Cards
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === "check" && styles.activeTab]}
          onPress={() => setActiveTab("check")}>
          <Text
            style={[
              styles.tabText,
              activeTab === "check" && styles.activeTabText,
            ]}>
            Check Compatibility
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tab, activeTab === "help" && styles.activeTab]}
          onPress={() => setActiveTab("help")}>
          <Text
            style={[
              styles.tabText,
              activeTab === "help" && styles.activeTabText,
            ]}>
            Help & Tips
          </Text>
        </TouchableOpacity>
      </ScrollView>

      {activeTab === "create" && <StudentCardManager />}
      {activeTab === "check" && <CardCompatibilityChecker />}
      {activeTab === "help" && <TroubleshootingGuide />}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },
  tabScrollView: {
    flexGrow: 0,
    marginHorizontal: 16,
    marginTop: 8,
  },
  tabContainer: {
    flexDirection: "row",
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  tab: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: "center",
    marginHorizontal: 2,
  },
  activeTab: {
    backgroundColor: "#007AFF",
  },
  tabText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#666",
  },
  activeTabText: {
    color: "#fff",
  },
});
