import ActionButtons from "@/components/ui/ActionButtons";
import AmountSelector from "@/components/ui/AmountSelector";
import AppHeader from "@/components/ui/AppHeader";
import BalanceCard from "@/components/ui/BalanceCard";
import ScanTimeoutModal from "@/components/ui/ScanTimeoutModal";
import { StudentData } from "@/types/student";
import {
  NFCDataHandler,
  NFCErrorHandler,
  NFCScanningSession,
} from "@/utils/nfcUtils";
import React, { useState } from "react";
import { Alert, ScrollView, StyleSheet, Text, View } from "react-native";
import NfcManager, { NfcTech } from "react-native-nfc-manager";

export default function WalletScreen() {
  const [studentData, setStudentData] = useState<StudentData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [showAmountSelector, setShowAmountSelector] = useState(false);
  const [showScanModal, setShowScanModal] = useState(false);
  const [scanModalTitle, setScanModalTitle] = useState("Scan NFC Card");
  const [scanModalMessage, setScanModalMessage] = useState(
    "Hold your NFC card near the device to scan"
  );

  const readStudentCard = async (): Promise<StudentData> => {
    // Try NDEF first
    try {
      await NfcManager.requestTechnology(NfcTech.Ndef);
      const tag = await NfcManager.getTag();

      if (!tag || !tag.ndefMessage || tag.ndefMessage.length === 0) {
        throw new Error("No NDEF message found on card");
      }

      const record = tag.ndefMessage[0];
      const payload = record.payload;

      // Remove the language code prefix for text records
      const languageCodeLength = payload[0];
      const text = payload.slice(languageCodeLength + 1);
      const studentDataString = String.fromCharCode(...text);

      return NFCDataHandler.decodeStudentData(studentDataString);
    } catch (ndefError) {
      console.log(
        "NDEF read failed. Card may not be formatted or may not contain student data:",
        ndefError
      );
      throw new Error(
        "Unable to read student data from this card. Please ensure the card is properly formatted with student data."
      );
    }
  };

  const handleCheckBalance = async () => {
    try {
      setIsLoading(true);
      setStudentData(null);
      setScanModalTitle("Check Balance");
      setScanModalMessage(
        "Hold your NFC card near the device to check balance"
      );
      setShowScanModal(true);

      // Start scanning session with timeout
      await NFCScanningSession.startScanningSession(15000, () => {
        setShowScanModal(false);
        setIsLoading(false);
        Alert.alert("Timeout", "Scanning timed out. Please try again.");
      });

      const data = await readStudentCard();

      if (!NFCDataHandler.validateStudentData(data)) {
        throw new Error("Invalid student data on card");
      }

      setStudentData(data);
      setShowScanModal(false);
    } catch (error) {
      console.error("Error reading card:", error);
      const errorTitle = NFCErrorHandler.getErrorTitle(error);
      const errorMessage = NFCErrorHandler.getErrorMessage(error);
      Alert.alert(errorTitle, errorMessage);
      setShowScanModal(false);
    } finally {
      setIsLoading(false);
      NFCScanningSession.stopScanningSession();
    }
  };

  const handleLoadCredit = async () => {
    setShowAmountSelector(true);
  };

  const onSelectAmount = async (amount: number) => {
    try {
      setIsLoading(true);
      setScanModalTitle("Load Credit");
      setScanModalMessage(
        `Hold your NFC card near the device to load $${amount}`
      );
      setShowScanModal(true);

      // Start scanning session with timeout
      await NFCScanningSession.startScanningSession(15000, () => {
        setShowScanModal(false);
        setIsLoading(false);
        Alert.alert("Timeout", "Scanning timed out. Please try again.");
      });

      // Use unified operations to start card session
      const { NFCUnifiedOperations } = await import("@/utils/nfcUtils");
      const currentData = await NFCUnifiedOperations.startCardSession();

      if (!NFCDataHandler.validateStudentData(currentData)) {
        throw new Error("Invalid student data on card");
      }

      setScanModalTitle("Confirm Transaction");
      setScanModalMessage("Keep your card near the device...");

      Alert.alert(
        "Confirm Credit Load",
        `Student: ${
          currentData.name
        }\nCurrent Balance: $${currentData.balance.toFixed(
          2
        )}\nAmount to Add: $${amount.toFixed(2)}\nNew Balance: $${(
          currentData.balance + amount
        ).toFixed(2)}`,
        [
          {
            text: "Cancel",
            onPress: async () => {
              await NFCUnifiedOperations.endCardSession();
              NFCScanningSession.stopScanningSession();
              setShowScanModal(false);
              setIsLoading(false);
            },
            style: "cancel",
          },
          {
            text: "Confirm",
            onPress: () => processCredit(currentData, amount),
          },
        ]
      );
    } catch (error) {
      console.error("Error reading card for credit:", error);
      const errorTitle = NFCErrorHandler.getErrorTitle(error);
      const errorMessage = NFCErrorHandler.getErrorMessage(error);
      Alert.alert(errorTitle, errorMessage);
      setShowScanModal(false);
      setIsLoading(false);
      NFCScanningSession.stopScanningSession();
    }
  };

  const processCredit = async (currentData: StudentData, amount: number) => {
    try {
      setScanModalTitle("Writing to Card");
      setScanModalMessage("Keep your card near the device while writing...");
      setShowScanModal(true);

      const updatedData: StudentData = {
        ...currentData,
        balance: currentData.balance + amount,
      };

      // Use unified operations to write to the current session
      const { NFCUnifiedOperations } = await import("@/utils/nfcUtils");
      await NFCUnifiedOperations.writeToCurrentSession(updatedData);

      setStudentData(updatedData);
      setShowScanModal(false);

      Alert.alert(
        "Credit Added Successfully",
        `Amount added: $${amount.toFixed(
          2
        )}\nNew balance: $${updatedData.balance.toFixed(2)}`,
        [{ text: "OK" }]
      );
    } catch (error) {
      console.error("Error processing credit:", error);
      const errorTitle = NFCErrorHandler.getErrorTitle(error);
      const errorMessage = NFCErrorHandler.getErrorMessage(error);
      Alert.alert(errorTitle, errorMessage);
      setShowScanModal(false);
    } finally {
      setIsLoading(false);
      const { NFCUnifiedOperations } = await import("@/utils/nfcUtils");
      await NFCUnifiedOperations.endCardSession();
      NFCScanningSession.stopScanningSession();
    }
  };

  const actionButtons = [
    {
      title: "Check Balance",
      icon: "search" as const,
      onPress: handleCheckBalance,
      loading: isLoading,
      variant: "primary" as const,
    },
    {
      title: "Load Credit",
      icon: "add-circle" as const,
      onPress: handleLoadCredit,
      loading: isLoading,
      variant: "success" as const,
    },
  ];

  return (
    <View style={styles.container}>
      <AppHeader
        title="Student Wallet"
        subtitle="Check balance and load credit"
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {studentData ? (
          <View style={styles.balanceSection}>
            <BalanceCard
              balance={studentData.balance}
              studentName={studentData.name}
              studentId={studentData.student_id}
              style={styles.balanceCard}
            />

            <View style={styles.detailsCard}>
              <Text style={styles.detailsTitle}>Card Details</Text>
              <View style={styles.detailRow}>
                <Text style={styles.detailLabel}>Total Transactions:</Text>
                <Text style={styles.detailValue}>{studentData.txn_count}</Text>
              </View>
            </View>
          </View>
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyTitle}>No Card Data</Text>
            <Text style={styles.emptySubtitle}>
              Tap &quot;Check Balance&quot; and scan a student card to view
              details
            </Text>
          </View>
        )}

        <ActionButtons buttons={actionButtons} columns={2} />
      </ScrollView>

      <AmountSelector
        visible={showAmountSelector}
        onClose={() => setShowAmountSelector(false)}
        onSelectAmount={onSelectAmount}
      />

      <ScanTimeoutModal
        visible={showScanModal}
        onCancel={() => {
          setShowScanModal(false);
          setIsLoading(false);
          NFCScanningSession.stopScanningSession();
        }}
        title={scanModalTitle}
        message={scanModalMessage}
        timeoutSeconds={15}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
  },
  content: {
    flex: 1,
    padding: 20,
  },
  balanceSection: {
    marginBottom: 32,
  },
  balanceCard: {
    marginBottom: 20,
  },
  detailsCard: {
    backgroundColor: "#ffffff",
    padding: 20,
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  detailsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1e293b",
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  detailLabel: {
    fontSize: 16,
    color: "#64748b",
    fontWeight: "500",
  },
  detailValue: {
    fontSize: 16,
    color: "#1e293b",
    fontWeight: "600",
  },
  emptyState: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 60,
    marginBottom: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1e293b",
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: "#64748b",
    textAlign: "center",
    lineHeight: 24,
  },
});
