import {
  DEFAULT_TERMINALS,
  StudentData,
  Terminal,
  Transaction,
} from "@/types/student";
import {
  NFCCardO<PERSON><PERSON>,
  NFCDataHandler,
  NFCErrorHandler,
  NFCScanningSession,
  TransactionLogger,
} from "@/utils/nfcUtils";
import React, { useEffect, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import NfcManager, { Ndef, NfcTech } from "react-native-nfc-manager";
import ScanTimeoutModal from "./ui/ScanTimeoutModal";

interface NFCTerminalProps {
  selectedTerminal: Terminal;
  onTerminalChange: (terminal: Terminal) => void;
}

const NFCTerminal: React.FC<NFCTerminalProps> = ({
  selectedTerminal,
  onTerminalChange,
}) => {
  const [isSupported, setIsSupported] = useState(false);
  const [isEnabled, setIsEnabled] = useState(false);
  const [studentData, setStudentData] = useState<StudentData | null>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [isWriting, setIsWriting] = useState(false);
  const [showScanModal, setShowScanModal] = useState(false);
  const [lastTransaction, setLastTransaction] = useState<Transaction | null>(
    null
  );
  const [todaysTransactions, setTodaysTransactions] = useState<Transaction[]>(
    []
  );

  useEffect(() => {
    const initNfc = async () => {
      try {
        const supported = await NfcManager.isSupported();
        setIsSupported(supported);

        if (supported) {
          await NfcManager.start();
          const enabled = await NfcManager.isEnabled();
          setIsEnabled(enabled);
        }
      } catch (error) {
        console.warn("NFC initialization error:", error);
      }
    };

    initNfc();
    loadTodaysTransactions();

    return () => {
      // Cleanup NFC
      NfcManager.cancelTechnologyRequest().catch(() => {});
    };
  }, []);

  const loadTodaysTransactions = async () => {
    const transactions = await TransactionLogger.getTodaysTransactions();
    setTodaysTransactions(transactions);
  };

  const readStudentCard = async (): Promise<StudentData> => {
    try {
      return await NFCCardOperations.readCardWithFallback();
    } catch (error) {
      // If the enhanced method fails, fall back to original method
      console.log("Enhanced read failed, trying original method:", error);

      // Try NDEF first
      try {
        await NfcManager.requestTechnology(NfcTech.Ndef);
        const tag = await NfcManager.getTag();

        if (!tag || !tag.ndefMessage || tag.ndefMessage.length === 0) {
          throw new Error("No NDEF message found on card");
        }

        const record = tag.ndefMessage[0];
        const payload = record.payload;

        // Remove the language code prefix for text records
        const languageCodeLength = payload[0];
        const text = payload.slice(languageCodeLength + 1);
        const studentDataString = String.fromCharCode(...text);

        return NFCDataHandler.decodeStudentData(studentDataString);
      } catch (ndefError) {
        console.log(
          "NDEF read failed. Card may not be formatted or may not contain student data:",
          ndefError
        );
        throw new Error(
          "Unable to read student data from this card. Please ensure the card is properly formatted with student data."
        );
      }
    }
  };

  const writeStudentCard = async (data: StudentData): Promise<void> => {
    try {
      await NFCCardOperations.writeCardWithFallback(data);
    } catch (error) {
      // If the enhanced method fails, fall back to original method
      console.log("Enhanced write failed, trying original method:", error);

      const dataString = NFCDataHandler.encodeStudentData(data);
      const bytes = Ndef.encodeMessage([Ndef.textRecord(dataString)]);

      if (!bytes) {
        throw new Error("Failed to encode NDEF message");
      }

      // Try NDEF write first
      try {
        await NfcManager.ndefHandler.writeNdefMessage(bytes);
      } catch (ndefError) {
        console.log("NDEF write failed, trying to format card:", ndefError);

        // Cancel current technology request
        await NfcManager.cancelTechnologyRequest();

        // Try to format the card using NdefFormatable technology
        try {
          await NfcManager.requestTechnology(NfcTech.NdefFormatable);
          await NfcManager.ndefFormatableHandlerAndroid.formatNdef(bytes);
        } catch (formatError) {
          console.error("Format failed:", formatError);
          throw new Error(
            "Unable to write to this card. The card may not be writable."
          );
        }
      }
    }
  };

  const handleCardTap = async () => {
    try {
      setIsScanning(true);
      setStudentData(null);
      setLastTransaction(null);
      setShowScanModal(true);

      // Start scanning session with timeout
      await NFCScanningSession.startScanningSession(15000, () => {
        setShowScanModal(false);
        setIsScanning(false);
        Alert.alert("Timeout", "Scanning timed out. Please try again.");
      });

      // Read current data from card
      const currentData = await readStudentCard();

      if (!NFCDataHandler.validateStudentData(currentData)) {
        throw new Error("Invalid student data on card");
      }

      setStudentData(currentData);
      setShowScanModal(false);

      // Check if balance is sufficient
      if (currentData.balance < selectedTerminal.transaction_cost) {
        Alert.alert(
          "Insufficient Balance",
          `Current balance: $${currentData.balance.toFixed(
            2
          )}\nRequired: $${selectedTerminal.transaction_cost.toFixed(2)}`,
          [
            {
              text: "OK",
              onPress: () => NFCScanningSession.stopScanningSession(),
            },
          ]
        );
        return;
      }

      // Show confirmation dialog
      Alert.alert(
        "Confirm Transaction",
        `Student: ${
          currentData.name
        }\nCurrent Balance: $${currentData.balance.toFixed(
          2
        )}\nTransaction Cost: $${selectedTerminal.transaction_cost.toFixed(
          2
        )}\nNew Balance: $${(
          currentData.balance - selectedTerminal.transaction_cost
        ).toFixed(2)}`,
        [
          {
            text: "Cancel",
            onPress: () => NFCScanningSession.stopScanningSession(),
            style: "cancel",
          },
          {
            text: "Confirm",
            onPress: () => processTransaction(currentData),
          },
        ]
      );
    } catch (error) {
      console.error("Error reading card:", error);
      const errorTitle = NFCErrorHandler.getErrorTitle(error);
      const errorMessage = NFCErrorHandler.getErrorMessage(error);
      Alert.alert(errorTitle, errorMessage);
      setShowScanModal(false);
      NFCScanningSession.stopScanningSession();
    } finally {
      setIsScanning(false);
    }
  };

  const processTransaction = async (currentData: StudentData) => {
    try {
      setIsWriting(true);
      setShowScanModal(true);

      // Process the transaction
      const { updatedData, transaction } = NFCDataHandler.processTransaction(
        currentData,
        selectedTerminal.transaction_cost,
        selectedTerminal.id
      );

      // Write updated data back to card
      await writeStudentCard(updatedData);

      // Log transaction locally
      await TransactionLogger.logTransaction(transaction);

      setStudentData(updatedData);
      setLastTransaction(transaction);
      await loadTodaysTransactions();
      setShowScanModal(false);

      Alert.alert(
        "Transaction Successful",
        `Amount deducted: $${selectedTerminal.transaction_cost.toFixed(
          2
        )}\nNew balance: $${updatedData.balance.toFixed(2)}`,
        [{ text: "OK" }]
      );
    } catch (error) {
      console.error("Error processing transaction:", error);
      const errorTitle = NFCErrorHandler.getErrorTitle(error);
      const errorMessage = NFCErrorHandler.getErrorMessage(error);
      Alert.alert(errorTitle, errorMessage);
      setShowScanModal(false);
    } finally {
      setIsWriting(false);
      NFCScanningSession.stopScanningSession();
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const loadCreditToCard = async () => {
    Alert.prompt(
      "Load Credit",
      "Enter amount to add to card (e.g., 25.00)",
      async (input) => {
        if (!input) return;

        const amount = parseFloat(input.trim());
        if (isNaN(amount) || amount <= 0) {
          Alert.alert("Error", "Please enter a valid amount greater than 0");
          return;
        }

        try {
          setIsWriting(true);
          await NfcManager.requestTechnology(NfcTech.Ndef);

          // Read current data from card
          const currentData = await readStudentCard();

          if (!NFCDataHandler.validateStudentData(currentData)) {
            throw new Error("Invalid student data on card");
          }

          // Show confirmation dialog
          Alert.alert(
            "Confirm Credit Load",
            `Student: ${
              currentData.name
            }\nCurrent Balance: $${currentData.balance.toFixed(
              2
            )}\nAmount to Add: $${amount.toFixed(2)}\nNew Balance: $${(
              currentData.balance + amount
            ).toFixed(2)}`,
            [
              {
                text: "Cancel",
                onPress: () => NfcManager.cancelTechnologyRequest(),
                style: "cancel",
              },
              {
                text: "Confirm",
                onPress: () => processCredit(currentData, amount),
              },
            ]
          );
        } catch (error) {
          console.error("Error reading card for credit:", error);
          const errorMessage =
            error instanceof Error ? error.message : "Unknown error occurred";
          Alert.alert(
            "Error Reading Card",
            `Failed to read card: ${errorMessage}`
          );
          NfcManager.cancelTechnologyRequest();
          setIsWriting(false);
        }
      },
      "plain-text",
      "25.00"
    );
  };

  const processCredit = async (currentData: StudentData, amount: number) => {
    try {
      const updatedData: StudentData = {
        ...currentData,
        balance: currentData.balance + amount,
      };

      // Write updated data back to card
      await writeStudentCard(updatedData);

      // Create a credit transaction record
      const transaction: Transaction = {
        id: `credit_${Date.now()}_${currentData.student_id}`,
        timestamp: new Date().toISOString(),
        student_id: currentData.student_id,
        student_name: currentData.name,
        old_balance: currentData.balance,
        new_balance: updatedData.balance,
        amount_deducted: -amount, // Negative for credit
        terminal_id: "credit_terminal",
      };

      // Log transaction locally
      await TransactionLogger.logTransaction(transaction);

      setStudentData(updatedData);
      setLastTransaction(transaction);
      await loadTodaysTransactions();

      Alert.alert(
        "Credit Added Successfully",
        `Amount added: $${amount.toFixed(
          2
        )}\nNew balance: $${updatedData.balance.toFixed(2)}`,
        [{ text: "OK" }]
      );
    } catch (error) {
      console.error("Error processing credit:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      Alert.alert("Credit Failed", `Error: ${errorMessage}`);
    } finally {
      setIsWriting(false);
      NfcManager.cancelTechnologyRequest();
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const writeNewStudentCard = async () => {
    Alert.prompt(
      "Create New Student Card",
      "Enter student details (format: ID,Name,Balance)",
      (input) => {
        if (!input) return;

        const parts = input.split(",");
        if (parts.length !== 3) {
          Alert.alert("Error", "Please use format: ID,Name,Balance");
          return;
        }

        const [id, name, balanceStr] = parts.map((p) => p.trim());
        const balance = parseFloat(balanceStr);

        if (isNaN(balance) || balance < 0) {
          Alert.alert("Error", "Invalid balance amount");
          return;
        }

        const newStudentData: StudentData = {
          student_id: id,
          name: name,
          balance: balance,
          txn_count: 0,
        };

        createNewCard(newStudentData);
      },
      "plain-text",
      "S12345,John Doe,50.00"
    );
  };

  const createNewCard = async (data: StudentData) => {
    try {
      setIsWriting(true);
      await NfcManager.requestTechnology(NfcTech.Ndef);
      await writeStudentCard(data);

      setStudentData(data);
      Alert.alert("Success", "New student card created successfully!");
    } catch (error) {
      console.error("Error creating card:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      Alert.alert(
        "Error Creating Card",
        `Failed to create card: ${errorMessage}`
      );
    } finally {
      setIsWriting(false);
      NfcManager.cancelTechnologyRequest();
    }
  };

  if (!isSupported) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>
          NFC is not supported on this device
        </Text>
      </View>
    );
  }

  if (!isEnabled) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>NFC is not enabled</Text>
        <Text style={styles.infoText}>
          Please enable NFC in your device settings
        </Text>
      </View>
    );
  }

  return (
    <>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Terminal Selection */}
        <View style={styles.terminalSection}>
          <Text style={styles.sectionTitle}>Select Terminal</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.terminalScroll}>
            {DEFAULT_TERMINALS.map((terminal) => (
              <TouchableOpacity
                key={terminal.id}
                style={[
                  styles.terminalButton,
                  selectedTerminal.id === terminal.id &&
                    styles.terminalButtonSelected,
                ]}
                onPress={() => onTerminalChange(terminal)}
                activeOpacity={0.8}>
                <Text
                  style={[
                    styles.terminalButtonText,
                    selectedTerminal.id === terminal.id &&
                      styles.terminalButtonTextSelected,
                  ]}>
                  {terminal.name}
                </Text>
                <Text
                  style={[
                    styles.terminalCost,
                    selectedTerminal.id === terminal.id &&
                      styles.terminalCostSelected,
                  ]}>
                  ${terminal.transaction_cost.toFixed(2)}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Current Terminal Info */}
        <View style={styles.currentTerminalSection}>
          <Text style={styles.currentTerminalTitle}>
            Current Terminal: {selectedTerminal.name}
          </Text>
          <Text style={styles.currentTerminalCost}>
            Transaction Cost: ${selectedTerminal.transaction_cost.toFixed(2)}
          </Text>
        </View>

        {/* Action Buttons */}
        <View style={styles.buttonSection}>
          <TouchableOpacity
            style={[
              styles.button,
              styles.primaryButton,
              (isScanning || isWriting) && styles.buttonDisabled,
            ]}
            onPress={handleCardTap}
            disabled={isScanning || isWriting}>
            {isScanning ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text style={styles.buttonText}>Tap Student Card</Text>
            )}
          </TouchableOpacity>
        </View>

        {/* Student Data Display */}
        {studentData && (
          <View style={styles.dataSection}>
            <Text style={styles.sectionTitle}>Student Information</Text>
            <View style={styles.dataCard}>
              <Text style={styles.dataLabel}>
                ID:{" "}
                <Text style={styles.dataValue}>{studentData.student_id}</Text>
              </Text>
              <Text style={styles.dataLabel}>
                Name: <Text style={styles.dataValue}>{studentData.name}</Text>
              </Text>
              <Text style={styles.dataLabel}>
                Balance:{" "}
                <Text style={styles.dataValue}>
                  ${studentData.balance.toFixed(2)}
                </Text>
              </Text>
              <Text style={styles.dataLabel}>
                Total Transactions:{" "}
                <Text style={styles.dataValue}>{studentData.txn_count}</Text>
              </Text>
            </View>
          </View>
        )}

        {/* Last Transaction */}
        {lastTransaction && (
          <View style={styles.dataSection}>
            <Text style={styles.sectionTitle}>Last Transaction</Text>
            <View style={styles.dataCard}>
              <Text style={styles.dataLabel}>
                Amount:{" "}
                <Text style={styles.dataValue}>
                  -${lastTransaction.amount_deducted.toFixed(2)}
                </Text>
              </Text>
              <Text style={styles.dataLabel}>
                Time:{" "}
                <Text style={styles.dataValue}>
                  {new Date(lastTransaction.timestamp).toLocaleString()}
                </Text>
              </Text>
              <Text style={styles.dataLabel}>
                Terminal:{" "}
                <Text style={styles.dataValue}>
                  {lastTransaction.terminal_id}
                </Text>
              </Text>
            </View>
          </View>
        )}

        {/* Today's Transactions Summary */}
        <View style={styles.dataSection}>
          <Text style={styles.sectionTitle}>Today&apos;s Summary</Text>
          <View style={styles.dataCard}>
            <Text style={styles.dataLabel}>
              Total Transactions:{" "}
              <Text style={styles.dataValue}>{todaysTransactions.length}</Text>
            </Text>
            <Text style={styles.dataLabel}>
              Total Amount:{" "}
              <Text style={styles.dataValue}>
                $
                {todaysTransactions
                  .reduce((sum, t) => sum + t.amount_deducted, 0)
                  .toFixed(2)}
              </Text>
            </Text>
          </View>
        </View>
      </ScrollView>

      <ScanTimeoutModal
        visible={showScanModal}
        onCancel={() => {
          setShowScanModal(false);
          setIsScanning(false);
          setIsWriting(false);
          NFCScanningSession.stopScanningSession();
        }}
        title={isWriting ? "Processing Transaction" : "Scan NFC Card"}
        message={
          isWriting
            ? "Keep your card near the device while processing..."
            : "Hold your NFC card near the device to scan"
        }
        timeoutSeconds={15}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
    padding: 20,
  },
  terminalSection: {
    marginBottom: 24,
  },
  terminalScroll: {
    paddingVertical: 8,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: "700",
    marginBottom: 16,
    color: "#1e293b",
    letterSpacing: -0.5,
  },
  terminalButton: {
    backgroundColor: "white",
    padding: 18,
    borderRadius: 16,
    marginRight: 16,
    minWidth: 140,
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#e2e8f0",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  terminalButtonSelected: {
    borderColor: "#3b82f6",
    backgroundColor: "#3b82f6",
    shadowColor: "#3b82f6",
    shadowOpacity: 0.25,
  },
  terminalButtonText: {
    fontSize: 15,
    fontWeight: "600",
    color: "#374151",
  },
  terminalButtonTextSelected: {
    color: "white",
  },
  terminalCost: {
    fontSize: 13,
    color: "#6b7280",
    marginTop: 6,
    fontWeight: "500",
  },
  terminalCostSelected: {
    color: "rgba(255, 255, 255, 0.9)",
  },
  currentTerminalSection: {
    backgroundColor: "white",
    padding: 20,
    borderRadius: 16,
    marginBottom: 24,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: "#f1f5f9",
  },
  currentTerminalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1e293b",
    marginBottom: 6,
  },
  currentTerminalCost: {
    fontSize: 16,
    color: "#64748b",
    fontWeight: "500",
  },
  buttonSection: {
    marginBottom: 24,
    gap: 16,
  },
  button: {
    padding: 18,
    borderRadius: 16,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  primaryButton: {
    backgroundColor: "#3b82f6",
  },
  secondaryButton: {
    backgroundColor: "white",
    borderWidth: 2,
    borderColor: "#3b82f6",
  },
  creditButton: {
    backgroundColor: "#10b981",
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    fontSize: 17,
    fontWeight: "600",
    color: "white",
  },
  secondaryButtonText: {
    color: "#3b82f6",
  },
  dataSection: {
    marginBottom: 24,
  },
  dataCard: {
    backgroundColor: "white",
    padding: 20,
    borderRadius: 16,
    borderLeftWidth: 5,
    borderLeftColor: "#3b82f6",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  dataLabel: {
    fontSize: 15,
    color: "#64748b",
    marginBottom: 10,
    fontWeight: "500",
  },
  dataValue: {
    color: "#1e293b",
    fontWeight: "600",
    fontSize: 16,
  },
  errorText: {
    fontSize: 20,
    color: "#ef4444",
    textAlign: "center",
    marginBottom: 12,
    fontWeight: "600",
  },
  infoText: {
    fontSize: 16,
    color: "#64748b",
    textAlign: "center",
    lineHeight: 24,
  },
});

export default NFCTerminal;
