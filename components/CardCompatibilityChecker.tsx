import { NFCCardOperations, NFCScanningSession } from "@/utils/nfcUtils";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Alert,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import ScanTimeoutModal from "./ui/ScanTimeoutModal";

const CardCompatibilityChecker: React.FC = () => {
  const [isChecking, setIsChecking] = useState(false);
  const [showScanModal, setShowScanModal] = useState(false);
  const [result, setResult] = useState<string | null>(null);

  const checkCard = async () => {
    try {
      setIsChecking(true);
      setResult(null);
      setShowScanModal(true);

      // Start scanning session with timeout
      await NFCScanningSession.startScanningSession(15000, () => {
        setShowScanModal(false);
        setIsChecking(false);
        Alert.alert("Timeout", "Scanning timed out. Please try again.");
      });

      const compatibilityResult =
        await NFCCardOperations.checkCardCompatibility();
      setResult(compatibilityResult);
      setShowScanModal(false);

      // Show detailed result
      let message = "";
      let title = "";

      switch (compatibilityResult) {
        case "NDEF card with data":
          title = "✅ Compatible Card";
          message =
            "This card contains NDEF data and is fully compatible with the student card system.";
          break;
        case "NDEF card (empty)":
          title = "✅ Compatible Card";
          message =
            "This is an empty NDEF card that can be used to create student cards.";
          break;
        case "Formatable card":
          title = "⚠️ Partially Compatible";
          message =
            "This card can be formatted for use with the student card system.";
          break;
        case "NfcA card (limited compatibility)":
          title = "❌ Limited Compatibility";
          message =
            "This card has limited compatibility. It may work but could have issues. Consider using an NDEF-compatible card.";
          break;
        default:
          title = "❌ Incompatible Card";
          message =
            "This card is not compatible with the student card system. Please use an NDEF-compatible NFC card or tag.";
      }

      Alert.alert(title, message);
    } catch (error) {
      console.error("Error checking card compatibility:", error);
      Alert.alert(
        "Error",
        `Failed to check card compatibility: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
      setShowScanModal(false);
    } finally {
      setIsChecking(false);
      NFCScanningSession.stopScanningSession();
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Card Compatibility Checker</Text>
        <Text style={styles.subtitle}>
          Check if your NFC card is compatible with the student card system
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.checkButton, isChecking && styles.checkButtonDisabled]}
        onPress={checkCard}
        disabled={isChecking}>
        {isChecking ? (
          <ActivityIndicator color="#fff" size="small" />
        ) : (
          <Text style={styles.checkButtonText}>Check Card Compatibility</Text>
        )}
      </TouchableOpacity>

      {result && (
        <View style={styles.resultContainer}>
          <Text style={styles.resultTitle}>Last Check Result:</Text>
          <Text style={styles.resultText}>{result}</Text>
        </View>
      )}

      <View style={styles.instructions}>
        <Text style={styles.instructionTitle}>Instructions:</Text>
        <Text style={styles.instructionText}>
          1. Tap "Check Card Compatibility"{"\n"}
          2. Hold your NFC card near the device{"\n"}
          3. Keep the card steady until the check completes{"\n"}
          4. Review the compatibility result
        </Text>
      </View>

      <ScanTimeoutModal
        visible={showScanModal}
        onCancel={() => {
          setShowScanModal(false);
          NFCScanningSession.stopScanningSession();
        }}
        isWriting={false}
        message="Hold your NFC card near the device to check compatibility..."
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: "#f5f5f5",
  },
  header: {
    marginBottom: 30,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    lineHeight: 22,
  },
  checkButton: {
    backgroundColor: "#007AFF",
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignItems: "center",
    marginBottom: 20,
  },
  checkButtonDisabled: {
    backgroundColor: "#999",
  },
  checkButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  resultContainer: {
    backgroundColor: "#fff",
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  resultTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  resultText: {
    fontSize: 14,
    color: "#666",
    fontFamily: "monospace",
  },
  instructions: {
    backgroundColor: "#fff",
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#e0e0e0",
  },
  instructionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 12,
  },
  instructionText: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
  },
});

export default CardCompatibilityChecker;
