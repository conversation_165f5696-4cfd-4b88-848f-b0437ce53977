import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useState } from "react";
import { Modal, StyleSheet, Text, TouchableOpacity, View } from "react-native";

interface ScanTimeoutModalProps {
  visible: boolean;
  onCancel: () => void;
  timeoutSeconds?: number;
  title?: string;
  message?: string;
}

const ScanTimeoutModal: React.FC<ScanTimeoutModalProps> = ({
  visible,
  onCancel,
  timeoutSeconds = 15,
  title = "Scan NFC Card",
  message = "Hold your NFC card near the device to scan",
}) => {
  const [remainingSeconds, setRemainingSeconds] = useState(timeoutSeconds);

  useEffect(() => {
    if (!visible) {
      setRemainingSeconds(timeoutSeconds);
      return;
    }

    const interval = setInterval(() => {
      setRemainingSeconds((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          onCancel();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [visible, timeoutSeconds, onCancel]);

  const progressPercentage = (remainingSeconds / timeoutSeconds) * 100;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onCancel}>
      <View style={styles.overlay}>
        <View style={styles.container}>
          <View style={styles.iconContainer}>
            <Ionicons name="card-outline" size={48} color="#3b82f6" />
          </View>

          <Text style={styles.title}>{title}</Text>
          <Text style={styles.message}>{message}</Text>

          <View style={styles.timerContainer}>
            <Text style={styles.timerText}>{remainingSeconds}s</Text>
            <View style={styles.progressBarContainer}>
              <View
                style={[
                  styles.progressBar,
                  { width: `${progressPercentage}%` },
                ]}
              />
            </View>
          </View>

          <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
            <Text style={styles.cancelText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    justifyContent: "center",
    alignItems: "center",
  },
  container: {
    backgroundColor: "#ffffff",
    borderRadius: 20,
    padding: 32,
    margin: 20,
    width: "85%",
    maxWidth: 350,
    alignItems: "center",
  },
  iconContainer: {
    backgroundColor: "#eff6ff",
    padding: 16,
    borderRadius: 50,
    marginBottom: 20,
  },
  title: {
    fontSize: 22,
    fontWeight: "600",
    color: "#1e293b",
    textAlign: "center",
    marginBottom: 8,
  },
  message: {
    fontSize: 16,
    color: "#64748b",
    textAlign: "center",
    lineHeight: 22,
    marginBottom: 24,
  },
  timerContainer: {
    alignItems: "center",
    marginBottom: 24,
    width: "100%",
  },
  timerText: {
    fontSize: 32,
    fontWeight: "700",
    color: "#3b82f6",
    marginBottom: 16,
  },
  progressBarContainer: {
    width: "100%",
    height: 6,
    backgroundColor: "#e2e8f0",
    borderRadius: 3,
    overflow: "hidden",
  },
  progressBar: {
    height: "100%",
    backgroundColor: "#3b82f6",
    borderRadius: 3,
  },
  cancelButton: {
    backgroundColor: "#f1f5f9",
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    minWidth: 120,
    alignItems: "center",
  },
  cancelText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#64748b",
  },
});

export default ScanTimeoutModal;
