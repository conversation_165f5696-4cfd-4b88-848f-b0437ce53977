import { Ionicons } from "@expo/vector-icons";
import React from "react";
import {
  ActivityIndicator,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

interface ActionButtonProps {
  title: string;
  icon: keyof typeof Ionicons.glyphMap;
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  variant?: "primary" | "secondary" | "success" | "outline";
}

interface ActionButtonsProps {
  buttons: ActionButtonProps[];
  columns?: number;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  title,
  icon,
  onPress,
  disabled = false,
  loading = false,
  variant = "outline",
}) => {
  const getButtonStyle = () => {
    switch (variant) {
      case "primary":
        return [styles.button, styles.primaryButton];
      case "secondary":
        return [styles.button, styles.secondaryButton];
      case "success":
        return [styles.button, styles.successButton];
      default:
        return [styles.button, styles.outlineButton];
    }
  };

  const getTextStyle = () => {
    switch (variant) {
      case "primary":
        return [styles.buttonText, styles.primaryText];
      case "secondary":
        return [styles.buttonText, styles.secondaryText];
      case "success":
        return [styles.buttonText, styles.successText];
      default:
        return [styles.buttonText, styles.outlineText];
    }
  };

  const getIconColor = () => {
    switch (variant) {
      case "primary":
      case "secondary":
      case "success":
        return "#ffffff";
      default:
        return "#3b82f6";
    }
  };

  return (
    <TouchableOpacity
      style={[
        ...getButtonStyle(),
        (disabled || loading) && styles.buttonDisabled,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}>
      <View style={styles.buttonContent}>
        {loading ? (
          <ActivityIndicator
            size="small"
            color={variant === "outline" ? "#3b82f6" : "#ffffff"}
          />
        ) : (
          <Ionicons name={icon} size={20} color={getIconColor()} />
        )}
        <Text style={getTextStyle()}>{title}</Text>
      </View>
    </TouchableOpacity>
  );
};

const ActionButtons: React.FC<ActionButtonsProps> = ({
  buttons,
  columns = 3,
}) => {
  return (
    <View style={styles.container}>
      <View
        style={[
          styles.grid,
          { flexDirection: columns === 1 ? "column" : "row" },
        ]}>
        {buttons.map((button, index) => (
          <View
            key={index}
            style={[
              styles.buttonWrapper,
              columns === 1 && styles.fullWidth,
              columns === 2 && styles.halfWidth,
              columns === 3 && styles.thirdWidth,
            ]}>
            <ActionButton {...button} />
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
  },
  grid: {
    flexWrap: "wrap",
    gap: 12,
  },
  buttonWrapper: {
    marginBottom: 12,
  },
  fullWidth: {
    width: "100%",
  },
  halfWidth: {
    width: "48%",
  },
  thirdWidth: {
    width: "31%",
  },
  button: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    minHeight: 60,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  primaryButton: {
    backgroundColor: "#3b82f6",
  },
  secondaryButton: {
    backgroundColor: "#6366f1",
  },
  successButton: {
    backgroundColor: "#10b981",
  },
  outlineButton: {
    backgroundColor: "#ffffff",
    borderWidth: 2,
    borderColor: "#e2e8f0",
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonContent: {
    alignItems: "center",
    gap: 8,
  },
  buttonText: {
    fontSize: 14,
    fontWeight: "600",
    textAlign: "center",
  },
  primaryText: {
    color: "#ffffff",
  },
  secondaryText: {
    color: "#ffffff",
  },
  successText: {
    color: "#ffffff",
  },
  outlineText: {
    color: "#3b82f6",
  },
});

export { ActionButton };
export default ActionButtons;
