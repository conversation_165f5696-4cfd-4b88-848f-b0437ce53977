import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { IconSymbol } from './IconSymbol';

interface BalanceCardProps {
  balance: number;
  studentName?: string;
  studentId?: string;
  onPress?: () => void;
  style?: ViewStyle;
  showDetails?: boolean;
}

const BalanceCard: React.FC<BalanceCardProps> = ({
  balance,
  studentName,
  studentId,
  onPress,
  style,
  showDetails = true,
}) => {
  const CardComponent = onPress ? TouchableOpacity : View;

  return (
    <CardComponent
      style={[styles.container, style]}
      onPress={onPress}
      activeOpacity={onPress ? 0.9 : 1}
    >
      <LinearGradient
        colors={['#10b981', '#059669']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        <View style={styles.content}>
          <View style={styles.header}>
            <Text style={styles.balanceLabel}>Balance</Text>
            <IconSymbol name="creditcard.fill" size={24} color="rgba(255, 255, 255, 0.8)" />
          </View>
          
          <Text style={styles.balanceAmount}>
            ${balance.toFixed(2)}
          </Text>
          
          {showDetails && studentId && (
            <Text style={styles.cardId}>
              ID {studentId}
            </Text>
          )}
          
          {showDetails && studentName && (
            <Text style={styles.studentName}>
              {studentName}
            </Text>
          )}
        </View>
      </LinearGradient>
    </CardComponent>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#10b981',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  gradient: {
    padding: 24,
    minHeight: 140,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  balanceLabel: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    fontWeight: '500',
  },
  balanceAmount: {
    fontSize: 32,
    fontWeight: '700',
    color: '#ffffff',
    letterSpacing: -1,
    marginBottom: 8,
  },
  cardId: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500',
    letterSpacing: 1,
  },
  studentName: {
    fontSize: 16,
    color: '#ffffff',
    fontWeight: '600',
    marginTop: 4,
  },
});

export default BalanceCard;
