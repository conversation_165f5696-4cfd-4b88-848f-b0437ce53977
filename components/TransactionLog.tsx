import { Transaction } from "@/types/student";
import { TransactionLogger } from "@/utils/nfcUtils";
import React, { useEffect, useState } from "react";
import {
  Alert,
  ScrollView,
  Share,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

const TransactionLog: React.FC = () => {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [todaysTransactions, setTodaysTransactions] = useState<Transaction[]>(
    []
  );
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTransactions();
  }, []);

  const loadTransactions = async () => {
    setLoading(true);
    try {
      const allTransactions = await TransactionLogger.getTransactions();
      const todaysTransactions =
        await TransactionLogger.getTodaysTransactions();

      setTransactions(allTransactions.reverse()); // Show newest first
      setTodaysTransactions(todaysTransactions);
    } catch (error) {
      console.error("Error loading transactions:", error);
    } finally {
      setLoading(false);
    }
  };

  const clearAllTransactions = () => {
    Alert.alert(
      "Clear All Transactions",
      "Are you sure you want to clear all transaction logs? This action cannot be undone.",
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Clear",
          style: "destructive",
          onPress: async () => {
            await TransactionLogger.clearTransactions();
            await loadTransactions();
          },
        },
      ]
    );
  };

  const exportTransactions = async () => {
    if (transactions.length === 0) {
      Alert.alert("No Data", "No transactions to export");
      return;
    }

    const csvHeader =
      "Timestamp,Student ID,Student Name,Old Balance,New Balance,Amount Deducted,Terminal ID\n";
    const csvData = transactions
      .map(
        (t) =>
          `"${t.timestamp}","${t.student_id}","${t.student_name}",${t.old_balance},${t.new_balance},${t.amount_deducted},"${t.terminal_id}"`
      )
      .join("\n");

    const csvContent = csvHeader + csvData;

    try {
      await Share.share({
        message: csvContent,
        title: "Transaction Log Export",
      });
    } catch (error) {
      console.error("Error sharing transactions:", error);
      Alert.alert("Error", "Failed to export transactions");
    }
  };

  const getTodaysStats = () => {
    const totalTransactions = todaysTransactions.length;
    const totalAmount = todaysTransactions.reduce(
      (sum, t) => sum + t.amount_deducted,
      0
    );
    const uniqueStudents = new Set(todaysTransactions.map((t) => t.student_id))
      .size;

    return { totalTransactions, totalAmount, uniqueStudents };
  };

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleDateString() + " " + date.toLocaleTimeString();
  };

  const stats = getTodaysStats();

  if (loading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.loadingText}>Loading transactions...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Today's Stats */}
      <View style={styles.statsSection}>
        <Text style={styles.sectionTitle}>Today's Statistics</Text>
        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{stats.totalTransactions}</Text>
            <Text style={styles.statLabel}>Transactions</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>
              ${stats.totalAmount.toFixed(2)}
            </Text>
            <Text style={styles.statLabel}>Total Amount</Text>
          </View>
          <View style={styles.statCard}>
            <Text style={styles.statNumber}>{stats.uniqueStudents}</Text>
            <Text style={styles.statLabel}>Unique Students</Text>
          </View>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionSection}>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={loadTransactions}>
          <Text style={styles.buttonText}>Refresh</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.exportButton}
          onPress={exportTransactions}>
          <Text style={styles.buttonText}>Export CSV</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.clearButton}
          onPress={clearAllTransactions}>
          <Text style={styles.buttonText}>Clear All</Text>
        </TouchableOpacity>
      </View>

      {/* Transaction List */}
      <View style={styles.transactionSection}>
        <Text style={styles.sectionTitle}>
          All Transactions ({transactions.length})
        </Text>

        {transactions.length === 0 ? (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>No transactions recorded yet</Text>
          </View>
        ) : (
          <ScrollView style={styles.transactionList}>
            {transactions.map((transaction) => (
              <View key={transaction.id} style={styles.transactionCard}>
                <View style={styles.transactionHeader}>
                  <Text style={styles.studentName}>
                    {transaction.student_name}
                  </Text>
                  <Text style={styles.transactionAmount}>
                    -${transaction.amount_deducted.toFixed(2)}
                  </Text>
                </View>

                <View style={styles.transactionDetails}>
                  <Text style={styles.transactionDetail}>
                    ID: {transaction.student_id}
                  </Text>
                  <Text style={styles.transactionDetail}>
                    Terminal: {transaction.terminal_id}
                  </Text>
                </View>

                <View style={styles.transactionBalance}>
                  <Text style={styles.balanceText}>
                    Balance: ${transaction.old_balance.toFixed(2)} → $
                    {transaction.new_balance.toFixed(2)}
                  </Text>
                </View>

                <Text style={styles.transactionTime}>
                  {formatDate(transaction.timestamp)}
                </Text>
              </View>
            ))}
          </ScrollView>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
    padding: 16,
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
  },
  statsSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 12,
    color: "#333",
  },
  statsGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  statCard: {
    backgroundColor: "white",
    padding: 16,
    borderRadius: 8,
    alignItems: "center",
    flex: 1,
    marginHorizontal: 4,
    borderLeftWidth: 4,
    borderLeftColor: "#007AFF",
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#007AFF",
  },
  statLabel: {
    fontSize: 12,
    color: "#666",
    marginTop: 4,
    textAlign: "center",
  },
  actionSection: {
    flexDirection: "row",
    marginBottom: 20,
    justifyContent: "space-between",
  },
  refreshButton: {
    backgroundColor: "#007AFF",
    padding: 12,
    borderRadius: 8,
    flex: 1,
    marginRight: 8,
    alignItems: "center",
  },
  exportButton: {
    backgroundColor: "#34C759",
    padding: 12,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 4,
    alignItems: "center",
  },
  clearButton: {
    backgroundColor: "#FF3B30",
    padding: 12,
    borderRadius: 8,
    flex: 1,
    marginLeft: 8,
    alignItems: "center",
  },
  buttonText: {
    color: "white",
    fontSize: 14,
    fontWeight: "bold",
  },
  transactionSection: {
    flex: 1,
  },
  transactionList: {
    flex: 1,
  },
  emptyState: {
    backgroundColor: "white",
    padding: 40,
    borderRadius: 8,
    alignItems: "center",
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
  },
  transactionCard: {
    backgroundColor: "white",
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: "#FF9500",
  },
  transactionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  studentName: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#333",
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#FF3B30",
  },
  transactionDetails: {
    marginBottom: 8,
  },
  transactionDetail: {
    fontSize: 14,
    color: "#666",
    marginBottom: 2,
  },
  transactionBalance: {
    marginBottom: 8,
  },
  balanceText: {
    fontSize: 14,
    color: "#666",
    fontFamily: "monospace",
  },
  transactionTime: {
    fontSize: 12,
    color: "#999",
    textAlign: "right",
  },
});

export default TransactionLog;
