import { StudentData } from "@/types/student";
import {
  NFCCard<PERSON>perations,
  NFCDataHandler,
  NFCErrorHandler,
  NFCScanningSession,
} from "@/utils/nfcUtils";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import NfcManager, { Ndef, NfcTech } from "react-native-nfc-manager";
import ScanTimeoutModal from "./ui/ScanTimeoutModal";

const StudentCardManager: React.FC = () => {
  const [studentId, setStudentId] = useState("");
  const [studentName, setStudentName] = useState("");
  const [initialBalance, setInitialBalance] = useState("");
  const [isWriting, setIsWriting] = useState(false);
  const [showScanModal, setShowScanModal] = useState(false);

  const validateForm = (): boolean => {
    if (!studentId.trim()) {
      Alert.alert("Error", "Student ID is required");
      return false;
    }
    if (!studentName.trim()) {
      Alert.alert("Error", "Student name is required");
      return false;
    }
    const balance = parseFloat(initialBalance);
    if (isNaN(balance) || balance < 0) {
      Alert.alert("Error", "Please enter a valid balance (minimum 0.00)");
      return false;
    }
    return true;
  };

  const createStudentCard = async () => {
    if (!validateForm()) return;

    const studentData: StudentData = {
      student_id: studentId.trim(),
      name: studentName.trim(),
      balance: parseFloat(initialBalance),
      txn_count: 0,
    };

    try {
      setIsWriting(true);
      setShowScanModal(true);

      // Start scanning session with timeout
      await NFCScanningSession.startScanningSession(15000, () => {
        setShowScanModal(false);
        setIsWriting(false);
        Alert.alert("Timeout", "Scanning timed out. Please try again.");
      });

      // Try enhanced write method first
      try {
        await NFCCardOperations.writeCardWithFallback(studentData);
        setShowScanModal(false);

        Alert.alert(
          "Success!",
          `Student card created successfully!\n\nStudent ID: ${
            studentData.student_id
          }\nName: ${studentData.name}\nBalance: $${studentData.balance.toFixed(
            2
          )}`,
          [
            {
              text: "Create Another",
              onPress: () => {
                setStudentId("");
                setStudentName("");
                setInitialBalance("");
              },
            },
            { text: "Done" },
          ]
        );
        return;
      } catch (enhancedError) {
        console.log(
          "Enhanced write failed, trying original method:",
          enhancedError
        );
      }

      // Fallback to original method
      // Encode student data first
      const dataString = NFCDataHandler.encodeStudentData(studentData);
      const bytes = Ndef.encodeMessage([Ndef.textRecord(dataString)]);

      if (!bytes) {
        throw new Error("Failed to encode NDEF message");
      }

      // Try NDEF first for already formatted cards
      let success = false;
      try {
        await NfcManager.requestTechnology(NfcTech.Ndef);
        await NfcManager.ndefHandler.writeNdefMessage(bytes);
        success = true;
      } catch (ndefError) {
        console.log("NDEF write failed, trying to format card:", ndefError);

        // Cancel current technology request
        await NfcManager.cancelTechnologyRequest();

        // Try to format the card using NdefFormatable technology
        try {
          await NfcManager.requestTechnology(NfcTech.NdefFormatable);
          await NfcManager.ndefFormatableHandlerAndroid.formatNdef(bytes);
          success = true;
        } catch (formatError) {
          console.log("Format failed, trying simple write:", formatError);

          // Cancel current technology request
          await NfcManager.cancelTechnologyRequest();

          // Try a simpler approach - just write raw data
          try {
            await NfcManager.requestTechnology([NfcTech.NfcA]);
            // For simple NfcA cards, we'll try to write the data directly
            throw new Error(
              "NfcA cards require special formatting. Please use an NDEF-compatible card."
            );
          } catch (nfcAError) {
            console.error("All write methods failed:", nfcAError);
            throw new Error(
              "Unable to write to this card. Please use an NDEF-compatible NFC card or tag."
            );
          }
        }
      }

      if (success) {
        setShowScanModal(false);

        Alert.alert(
          "Success!",
          `Student card created successfully!\n\nStudent ID: ${
            studentData.student_id
          }\nName: ${studentData.name}\nBalance: $${studentData.balance.toFixed(
            2
          )}`,
          [
            {
              text: "Create Another",
              onPress: () => {
                setStudentId("");
                setStudentName("");
                setInitialBalance("");
              },
            },
            { text: "Done" },
          ]
        );
      }
    } catch (error) {
      console.error("Error creating student card:", error);
      const errorTitle = NFCErrorHandler.getErrorTitle(error);
      const errorMessage = NFCErrorHandler.getErrorMessage(error);
      Alert.alert(errorTitle, errorMessage);
      setShowScanModal(false);
    } finally {
      setIsWriting(false);
      NFCScanningSession.stopScanningSession();
    }
  };

  const generateSampleData = () => {
    const sampleStudents = [
      { id: "S001", name: "John Doe", balance: "50.00" },
      { id: "S002", name: "Jane Smith", balance: "75.50" },
      { id: "S003", name: "Mike Johnson", balance: "100.00" },
      { id: "S004", name: "Sarah Wilson", balance: "25.75" },
      { id: "S005", name: "Alex Brown", balance: "60.25" },
    ];

    const randomStudent =
      sampleStudents[Math.floor(Math.random() * sampleStudents.length)];
    setStudentId(randomStudent.id);
    setStudentName(randomStudent.name);
    setInitialBalance(randomStudent.balance);
  };

  return (
    <>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        <View style={styles.form}>
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Student ID *</Text>
            <TextInput
              style={[styles.input, studentId && styles.inputFilled]}
              value={studentId}
              onChangeText={setStudentId}
              placeholder="e.g., S12345"
              maxLength={20}
              autoCapitalize="characters"
              placeholderTextColor="#94a3b8"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Student Name *</Text>
            <TextInput
              style={[styles.input, studentName && styles.inputFilled]}
              value={studentName}
              onChangeText={setStudentName}
              placeholder="e.g., John Doe"
              maxLength={50}
              autoCapitalize="words"
              placeholderTextColor="#94a3b8"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Initial Balance *</Text>
            <TextInput
              style={[styles.input, initialBalance && styles.inputFilled]}
              value={initialBalance}
              onChangeText={setInitialBalance}
              placeholder="e.g., 50.00"
              keyboardType="decimal-pad"
              maxLength={10}
              placeholderTextColor="#94a3b8"
            />
          </View>

          <TouchableOpacity
            style={styles.sampleButton}
            onPress={generateSampleData}>
            <Text style={styles.sampleButtonText}>Generate Sample Data</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.createButton, isWriting && styles.buttonDisabled]}
            onPress={createStudentCard}
            disabled={isWriting}
            activeOpacity={0.8}>
            {isWriting ? (
              <>
                <ActivityIndicator color="white" size="small" />
                <Text style={styles.buttonText}>Writing to Card...</Text>
              </>
            ) : (
              <Text style={styles.buttonText}>Create Student Card</Text>
            )}
          </TouchableOpacity>
        </View>

        <View style={styles.instructions}>
          <Text style={styles.instructionsTitle}>Instructions:</Text>
          <Text style={styles.instructionText}>
            1. Fill in all required fields above
          </Text>
          <Text style={styles.instructionText}>
            2. Tap &quot;Create Student Card&quot; button
          </Text>
          <Text style={styles.instructionText}>
            3. When prompted, place a blank NFC card near your device
          </Text>
          <Text style={styles.instructionText}>
            4. Keep the card in place until the write operation completes
          </Text>
          <Text style={styles.instructionText}>
            5. The card will now contain the student&apos;s information
          </Text>
        </View>
      </ScrollView>

      <ScanTimeoutModal
        visible={showScanModal}
        onCancel={() => {
          setShowScanModal(false);
          setIsWriting(false);
          NFCScanningSession.stopScanningSession();
        }}
        title="Write Student Card"
        message="Hold a blank NFC card near your device to write student data"
        timeoutSeconds={15}
      />
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f8fafc",
    padding: 20,
  },
  form: {
    backgroundColor: "#ffffff",
    padding: 24,
    borderRadius: 20,
    marginBottom: 28,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 5,
    borderWidth: 1,
    borderColor: "#f1f5f9",
  },
  inputGroup: {
    marginBottom: 24,
  },
  label: {
    fontSize: 17,
    fontWeight: "600",
    color: "#374151",
    marginBottom: 10,
  },
  input: {
    borderWidth: 2,
    borderColor: "#e2e8f0",
    borderRadius: 12,
    padding: 16,
    fontSize: 17,
    backgroundColor: "#f8fafc",
    color: "#1e293b",
    fontWeight: "500",
  },
  inputFilled: {
    borderColor: "#3b82f6",
    backgroundColor: "#ffffff",
  },
  sampleButton: {
    backgroundColor: "#f1f5f9",
    padding: 14,
    borderRadius: 12,
    alignItems: "center",
    marginBottom: 24,
    borderWidth: 2,
    borderColor: "#e2e8f0",
  },
  sampleButtonText: {
    color: "#3b82f6",
    fontSize: 15,
    fontWeight: "600",
  },
  createButton: {
    backgroundColor: "#3b82f6",
    padding: 18,
    borderRadius: 16,
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "center",
    shadowColor: "#3b82f6",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: "white",
    fontSize: 17,
    fontWeight: "600",
    marginLeft: 8,
  },
  instructions: {
    backgroundColor: "#ffffff",
    padding: 24,
    borderRadius: 20,
    marginBottom: 28,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: "#f1f5f9",
  },
  instructionsTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#1e293b",
    marginBottom: 16,
  },
  instructionText: {
    fontSize: 16,
    color: "#64748b",
    marginBottom: 12,
    lineHeight: 24,
    fontWeight: "400",
  },
  dataFormat: {
    backgroundColor: "#ffffff",
    padding: 24,
    borderRadius: 20,
    marginBottom: 28,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: "#f1f5f9",
  },
  dataFormatTitle: {
    fontSize: 20,
    fontWeight: "700",
    color: "#1e293b",
    marginBottom: 16,
  },
  codeBlock: {
    backgroundColor: "#f8fafc",
    padding: 20,
    borderRadius: 12,
    borderLeftWidth: 5,
    borderLeftColor: "#3b82f6",
  },
  codeText: {
    fontFamily: "monospace",
    fontSize: 13,
    color: "#374151",
    lineHeight: 20,
  },
});

export default StudentCardManager;
