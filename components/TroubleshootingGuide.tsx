import { NFCTroubleshootingTips } from "@/utils/nfcUtils";
import React, { useState } from "react";
import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

const TroubleshootingGuide: React.FC = () => {
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  const toggleSection = (section: string) => {
    setExpandedSection(expandedSection === section ? null : section);
  };

  const renderSection = (
    title: string,
    sectionKey: string,
    tips: string[],
    icon: string
  ) => (
    <View style={styles.section}>
      <TouchableOpacity
        style={styles.sectionHeader}
        onPress={() => toggleSection(sectionKey)}>
        <Text style={styles.sectionIcon}>{icon}</Text>
        <Text style={styles.sectionTitle}>{title}</Text>
        <Text style={styles.chevron}>
          {expandedSection === sectionKey ? "▼" : "▶"}
        </Text>
      </TouchableOpacity>

      {expandedSection === sectionKey && (
        <View style={styles.sectionContent}>
          {tips.map((tip, index) => (
            <View key={index} style={styles.tipItem}>
              <Text style={styles.tipNumber}>{index + 1}.</Text>
              <Text style={styles.tipText}>{tip}</Text>
            </View>
          ))}
        </View>
      )}
    </View>
  );

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>NFC Troubleshooting Guide</Text>
        <Text style={styles.subtitle}>
          Having issues with NFC? Here are some common solutions.
        </Text>
      </View>

      {renderSection(
        "Permission Issues",
        "permission",
        NFCTroubleshootingTips.getPermissionErrorTips(),
        "🔒"
      )}

      {renderSection(
        "Card Reading Problems",
        "reading",
        NFCTroubleshootingTips.getCardReadErrorTips(),
        "📖"
      )}

      {renderSection(
        "Card Writing Problems",
        "writing",
        NFCTroubleshootingTips.getCardWriteErrorTips(),
        "✏️"
      )}

      {renderSection(
        "Device-Specific Tips",
        "device",
        NFCTroubleshootingTips.getDeviceSpecificTips(),
        "📱"
      )}

      <View style={styles.footer}>
        <View style={styles.footerSection}>
          <Text style={styles.footerTitle}>Still having issues?</Text>
          <Text style={styles.footerText}>
            • Make sure your device supports NFC{"\n"}• Try using a different
            NFC card{"\n"}• Check if other NFC apps work on your device{"\n"}•
            Contact support if problems persist
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    padding: 20,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    lineHeight: 22,
  },
  section: {
    marginHorizontal: 16,
    marginBottom: 12,
    backgroundColor: "#fff",
    borderRadius: 12,
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  sectionIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  sectionTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  chevron: {
    fontSize: 12,
    color: "#666",
  },
  sectionContent: {
    padding: 16,
  },
  tipItem: {
    flexDirection: "row",
    marginBottom: 12,
  },
  tipNumber: {
    fontSize: 14,
    fontWeight: "600",
    color: "#007AFF",
    marginRight: 8,
    minWidth: 20,
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
  },
  footer: {
    margin: 16,
    padding: 16,
    backgroundColor: "#fff",
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  footerSection: {
    marginBottom: 16,
  },
  footerTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 8,
  },
  footerText: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
  },
});

export default TroubleshootingGuide;
